// 音频文件配置
// 您可以在这里添加、删除或重新排序音频文件
// 文件名应该放在 res/audios/ 目录下

const AUDIO_CONFIG = {
    // 音频文件列表（按播放顺序）
    files: [
        'umbrella.mp3',
        'Star_Rail.mp3'
    ],
    
    // 默认音量 (0.0 - 1.0)
    defaultVolume: 0.3,
    
    // 是否循环播放单个文件
    loopSingle: true,
    
    // 是否自动切换到下一首（当 loopSingle 为 false 时）
    autoNext: true
};

// 导出配置（如果在模块环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AUDIO_CONFIG;
}

// 全局变量（用于直接在HTML中引用）
window.AUDIO_CONFIG = AUDIO_CONFIG;

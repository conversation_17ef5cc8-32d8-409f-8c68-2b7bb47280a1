# 背景音乐快速更换说明

## 如何更换背景音乐

1. **准备音频文件**
   - 将您的音频文件（.mp3格式）放入 `res/audios/` 目录
   - 例如：`res/audios/my-music.mp3`

2. **修改配置**
   - 打开 `src/script.js` 文件
   - 找到第565行左右的代码：
     ```javascript
     this.audioFileName = 'umbrella'; // 修改这里的文件名（不含.mp3扩展名）
     ```
   - 将 `'umbrella'` 替换为您的音频文件名（不含.mp3扩展名）
   - 例如：`this.audioFileName = 'my-music';`

3. **保存并刷新**
   - 保存文件
   - 刷新浏览器页面
   - 点击音乐控制按钮即可播放新的背景音乐

## 示例

如果您有以下音频文件：
- `res/audios/peaceful-night.mp3`
- `res/audios/starry-sky.mp3`
- `res/audios/gentle-rain.mp3`

要使用 `peaceful-night.mp3`，只需修改：
```javascript
this.audioFileName = 'peaceful-night';
```

## 注意事项

- 音频文件必须是 .mp3 格式
- 文件名不要包含特殊字符或空格，建议使用英文和连字符
- 确保音频文件路径正确：`res/audios/文件名.mp3`
- 修改后需要刷新页面才能生效

## 程序化切换（高级用法）

如果您需要在运行时动态切换音频，可以使用以下方法：

```javascript
// 获取音乐控制器实例（需要在页面加载完成后）
const musicController = window.musicController; // 需要先在代码中暴露这个实例

// 切换到新的音频文件
musicController.setAudioFileName('new-music-name');
```
